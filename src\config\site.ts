export interface SiteConfig {
  // Personal Information
  name: string;
  title: string;
  description: string;
  email: string;
  domain: string;
  
  // Professional
  profession: string; // 'developer', 'designer', 'marketer', 'consultant', 'teacher'
  jobTitle: string;
  location: string;
  bio: string;
  
  // Social Links
  social: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
    website?: string;
  };
  
  // Navigation
  navigation: {
    name: string;
    href: string;
    icon?: string;
  }[];
  
  // SEO & Analytics
  seo: {
    ogImage: string;
    twitterCard: 'summary' | 'summary_large_image';
    keywords: string[];
  };
  
  // Features
  features: {
    darkMode: boolean;
    analytics: boolean;
    contactForm: boolean;
    blog: boolean;
  };
  
  // GitHub Configuration (for portfolio projects)
  github: {
    username: string;
    baseUrl: string;
  };
  
  // UI Labels for internationalization and customization
  labels: {
    navigation: {
      home: string;
      about: string;
      portfolio: string;
      resume: string;
      resources: string;
      contact: string;
    };
    sections: {
      aboutMe: string;
      featuredProjects: string;
      ctaHeading: string;
      getInTouch: string;
      explore: string;
    };
    buttons: {
      viewPortfolio: string;
      getInTouch: string;
      sendMessage: string;
      downloadResume: string;
      viewAllProjects: string;
      viewFullResume: string;
      startConversation: string;
      backToTop: string;
    };
  };
}

// User Configuration - Edit this section to customize your portfolio
export const siteConfig: SiteConfig = {
  // Personal Information
  name: "Your Name",
  title: "Your Name | Your Professional Title", 
  description: "Professional with expertise in creating exceptional results and solving complex challenges.",
  email: "<EMAIL>",
  domain: "https://yourdomain.dev",
  
  // Professional (Choose: 'developer', 'designer', 'marketer', 'consultant', 'teacher')
  profession: "developer",
  jobTitle: "Your Professional Title",
  location: "Your City, Country",
  bio: "Brief professional description that will be auto-generated based on your profession, or customize here.",
  
  // Social Links (remove or set to undefined to hide)
  social: {
    github: "https://github.com/yourusername",
    linkedin: "https://linkedin.com/in/yourusername", 
    twitter: "https://twitter.com/yourusername",
    // instagram: "https://instagram.com/yourusername",
    // youtube: "https://youtube.com/@yourusername",
    // website: "https://yourwebsite.com",
  },
  
  // Navigation Menu (will be populated with labels)
  navigation: [],
  
  // SEO Configuration
  seo: {
    ogImage: '/images/og-image.jpg',
    twitterCard: 'summary_large_image',
    keywords: [
      'software developer',
      'web developer', 
      'full stack developer',
      'portfolio',
      'javascript',
      'typescript',
      'react',
      'node.js'
    ],
  },
  
  // Feature Toggles
  features: {
    darkMode: true,
    analytics: false,
    contactForm: true,
    blog: false,
  },
  
  // GitHub Configuration (for portfolio projects)
  github: {
    username: "yourusername",
    baseUrl: "https://github.com/yourusername",
  },
  
  // UI Labels Configuration
  labels: {
    navigation: {
      home: "Home",
      about: "About",
      portfolio: "Portfolio", 
      resume: "Resume",
      resources: "Resources",
      contact: "Contact"
    },
    sections: {
      aboutMe: "About Me",
      featuredProjects: "Featured Projects",
      ctaHeading: "Ready to Build Something Amazing?",
      getInTouch: "Get in Touch",
      explore: "Explore"
    },
    buttons: {
      viewPortfolio: "View Portfolio",
      getInTouch: "Let's build something powerful",
      sendMessage: "Send Message",
      downloadResume: "Download Resume",
      viewAllProjects: "View All Projects",
      viewFullResume: "View Full Resume",
      startConversation: "Start a Conversation",
      backToTop: "Back to top"
    }
  }
};

// Utility function to get site config
export function getSiteConfig(): SiteConfig {
  return siteConfig;
}

// Helper functions for common config access
export const getSocialLinks = () => Object.entries(siteConfig.social).filter(([_, url]) => url);

// Generate navigation from labels
export const getNavigation = () => [
  { name: siteConfig.labels.navigation.home, href: '/' },
  { name: siteConfig.labels.navigation.about, href: '/about' },
  { name: siteConfig.labels.navigation.portfolio, href: '/portfolio' },
  { name: siteConfig.labels.navigation.resume, href: '/resume' },
  { name: siteConfig.labels.navigation.resources, href: '/resources' },
  { name: siteConfig.labels.navigation.contact, href: '/contact' },
];

export const getSEOConfig = () => siteConfig.seo;